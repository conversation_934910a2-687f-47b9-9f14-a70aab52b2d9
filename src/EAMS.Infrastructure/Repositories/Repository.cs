using EAMS.Domain.Interfaces;
using EAMS.Domain.Entities;
using EAMS.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Numerics;

namespace EAMS.Infrastructure.Repositories;
public class Repository<T> : IRepository<T> where T : BaseEntity
{
    protected readonly EamsDbContext _context;
    protected readonly DbSet<T> _dbSet;

    public Repository(EamsDbContext context)
    {
        _context = context;
        _dbSet = context.Set<T>();
    }

    public virtual async Task<T?> GetByIdAsync(Int64 id)
    {
        return await _dbSet.FirstOrDefaultAsync(e => e.Id == id);
    }

    public virtual async Task<IEnumerable<T>> GetAllAsync()
    {
        return await _dbSet.ToListAsync();
    }

    public virtual async Task<T> AddAsync(T entity)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        entity.CreatedAt = DateTime.UtcNow;
        entity.UpdatedAt = DateTime.UtcNow;

        var result = await _dbSet.AddAsync(entity);
        return result.Entity;
    }

    public virtual async Task<T> UpdateAsync(T entity)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        var existingEntity = await GetByIdAsync(entity.Id);
        if (existingEntity == null)
            throw new KeyNotFoundException($"Entity with ID {entity.Id} not found.");

        entity.UpdatedAt = DateTime.UtcNow;
        entity.CreatedAt = existingEntity.CreatedAt; // Preserve original creation date

        _context.Entry(existingEntity).CurrentValues.SetValues(entity);
        return existingEntity;
    }

    public virtual async Task<bool> DeleteAsync(Int64 id)
    {
        var entity = await GetByIdAsync(id);
        if (entity == null)
            return false;

        _dbSet.Remove(entity);
        return true;
    }

    public virtual async Task<bool> ExistsAsync(Int64 id)
    {
        return await _dbSet.AnyAsync(e => e.Id == id);
    }

    public virtual async Task<int> SaveChangesAsync()
    {
        return await _context.SaveChangesAsync();
    }
}