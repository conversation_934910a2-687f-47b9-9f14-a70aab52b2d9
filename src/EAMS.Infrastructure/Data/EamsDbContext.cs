using Microsoft.EntityFrameworkCore;
using EAMS.Domain.Entities;
using NetTopologySuite;
using NetTopologySuite.Geometries;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace EAMS.Infrastructure.Data;
public class EamsDbContext : DbContext
    {
        public DbSet<Accommodation> Accommodations { get; set; }
        public DbSet<Amenity> Amenities { get; set; }
        public DbSet<AmenityOptions> AmenityOptions { get; set; }

        public EamsDbContext(DbContextOptions<EamsDbContext> options) : base(options) {}


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfigurationsFromAssembly(typeof(EamsDbContext).Assembly);

            var gf = NtsGeometryServices.Instance.CreateGeometryFactory(4326);
            var converter = new ValueConverter<GeoPoint?, Point?>(
                to => to.HasValue
                        ? gf.CreatePoint(new Coordinate(to.Value.Longitude, to.Value.Latitude))
                        : null,
                from => from == null
                            ? (GeoPoint?)null
                            : GeoPoint.Create(from.Y, from.X),
                convertsNulls: true
            );
            var comparer = new ValueComparer<GeoPoint?>(
                (a,b) => a.Equals(b),
                v => v.HasValue ? HashCode.Combine(v.Value.Latitude, v.Value.Longitude) : 0,
                v => v
            );

            modelBuilder.Entity<Accommodation>(e =>
            {
                e.HasKey(x => x.Id);

                e.Property(x => x.Location)
                    .HasConversion(converter, comparer)
                    .HasColumnType("geography");
            });
        }
    }
