using Microsoft.EntityFrameworkCore.Migrations;
using NetTopologySuite.Geometries;

#nullable disable

namespace EAMS.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddLocationToAccommodations : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Street",
                table: "Accommodations",
                newName: "StreetLine1");

            migrationBuilder.AlterColumn<string>(
                name: "Duration",
                table: "Accommodations",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<Point>(
                name: "Location",
                table: "Accommodations",
                type: "geography",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "StreetLine2",
                table: "Accommodations",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Location",
                table: "Accommodations");

            migrationBuilder.DropColumn(
                name: "StreetLine2",
                table: "Accommodations");

            migrationBuilder.RenameColumn(
                name: "StreetLine1",
                table: "Accommodations",
                newName: "Street");

            migrationBuilder.AlterColumn<int>(
                name: "Duration",
                table: "Accommodations",
                type: "int",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");
        }
    }
}
