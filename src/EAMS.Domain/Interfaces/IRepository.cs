using EAMS.Domain.Entities;

namespace EAMS.Domain.Interfaces;

public interface IRepository<T> where T : BaseEntity
{
    // Read operations
    Task<T?> GetByIdAsync(Int64 id);
    Task<IEnumerable<T>> GetAllAsync();

    // Write operations
    Task<T> AddAsync(T entity);
    Task<T> UpdateAsync(T entity);
    Task<bool> DeleteAsync(Int64 id);
    Task<bool> ExistsAsync(Int64 id);

    // Unit of work
    Task<int> SaveChangesAsync();
}
